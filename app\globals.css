@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

body {
  font-family: var(--font-sans);
}

:root {
  --background: #ffffff;
  --foreground: #000000;
  --card: #ffffff;
  --card-foreground: #000000;
  --popover: #ffffff;
  --popover-foreground: #000000;
  --primary: #46413d;
  --primary-foreground: #ffffff;
  --secondary: #e2dac4;
  --secondary-foreground: #46413d;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #e2dac4;
  --accent-foreground: #46413d;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #46413d;
  --chart-1: #46413d;
  --chart-2: #e2dac4;
  --chart-3: #a1a1aa;
  --chart-4: #d4d4d8;
  --chart-5: #71717a;
  --radius: 0.625rem;
  --sidebar: #ffffff;
  --sidebar-foreground: #000000;
  --sidebar-primary: #46413d;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #e2dac4;
  --sidebar-accent-foreground: #46413d;
  --sidebar-border: #e5e5e5;
  --sidebar-ring: #46413d;
}

.dark {
  --background: #ffffff;
  --foreground: #000000;
  --card: #ffffff;
  --card-foreground: #000000;
  --popover: #ffffff;
  --popover-foreground: #000000;
  --primary: #46413d;
  --primary-foreground: #ffffff;
  --secondary: #e2dac4;
  --secondary-foreground: #46413d;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #e2dac4;
  --accent-foreground: #46413d;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #46413d;
  --chart-1: #46413d;
  --chart-2: #e2dac4;
  --chart-3: #a1a1aa;
  --chart-4: #d4d4d8;
  --chart-5: #71717a;
  --sidebar: #ffffff;
  --sidebar-foreground: #000000;
  --sidebar-primary: #46413d;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #e2dac4;
  --sidebar-accent-foreground: #46413d;
  --sidebar-border: #e5e5e5;
  --sidebar-ring: #46413d;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
