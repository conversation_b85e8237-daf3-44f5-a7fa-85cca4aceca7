{"name": "vid-analyzer-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && prisma db push && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@clerk/nextjs": "^6.12.4", "@prisma/client": "^6.4.1", "@radix-ui/react-slot": "^1.1.2", "@types/uuid": "^10.0.0", "axios": "^1.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.479.0", "next": "15.2.2", "prisma": "^6.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-modal": "^3.16.3", "stripe": "^17.7.0", "svix": "^1.61.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "youtubei.js": "^13.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-modal": "^3.16.3", "eslint": "^9", "eslint-config-next": "15.2.2", "tailwindcss": "^4", "typescript": "^5"}}