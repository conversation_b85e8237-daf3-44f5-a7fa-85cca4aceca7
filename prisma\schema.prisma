// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id        String     @id @default(uuid())
  email     String     @unique
  name      String?
  analyses  Analysis[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
}

model Analysis {
  id         String   @id @default(uuid())
  videoId    String
  videoTitle String?
  analysis   String   @db.Text
  userId     String
  user       User     @relation(fields: [userId], references: [id])
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}
