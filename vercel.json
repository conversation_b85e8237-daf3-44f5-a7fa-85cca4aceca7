{"version": 2, "buildCommand": "npm run build", "installCommand": "npm install", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/api/analyze/route.ts": {"memory": 1024, "maxDuration": 10}, "app/api/transcript/route.ts": {"memory": 1024, "maxDuration": 10}, "app/api/video-frames/route.ts": {"memory": 1024, "maxDuration": 10}, "app/api/local-analyses/route.ts": {"memory": 1024, "maxDuration": 10}, "app/api/local-analyses/[id]/route.ts": {"memory": 1024, "maxDuration": 10}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "ALLOWALL"}, {"key": "Content-Security-Policy", "value": "frame-ancestors *;"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, max-age=0"}]}], "env": {"NEXTJS_IGNORE_ESLINT": "1", "NEXTJS_IGNORE_TYPECHECK": "1"}}